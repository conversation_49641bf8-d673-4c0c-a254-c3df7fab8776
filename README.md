# WebSocket Demo 项目

这是一个基于SpringBoot 2.7.18和JDK 1.8的WebSocket演示项目，提供了完整的WebSocket服务端实现和测试页面。

## 项目特性

- ✅ 基于SpringBoot 2.7.18，兼容JDK 1.8
- ✅ WebSocket连接管理和消息处理
- ✅ 支持实时双向通信
- ✅ 消息广播功能
- ✅ 连接状态监控
- ✅ 完整的Web测试页面
- ✅ REST API接口
- ✅ 跨域支持

## 项目结构

```
websocket-demo/
├── src/
│   ├── main/
│   │   ├── java/com/example/websocket/
│   │   │   ├── WebSocketDemoApplication.java     # 主启动类
│   │   │   ├── config/
│   │   │   │   └── WebSocketConfig.java          # WebSocket配置
│   │   │   ├── handler/
│   │   │   │   └── WebSocketHandler.java         # WebSocket处理器
│   │   │   └── controller/
│   │   │       └── WebSocketController.java      # REST控制器
│   │   └── resources/
│   │       ├── static/
│   │       │   └── index.html                    # 测试页面
│   │       └── application.properties            # 应用配置
│   └── test/
│       └── java/com/example/websocket/
│           └── WebSocketDemoApplicationTests.java
├── pom.xml                                       # Maven配置
└── README.md                                     # 项目说明
```

## 快速开始

### 1. 环境要求

- JDK 1.8+
- Maven 3.6+

### 2. 运行项目

```bash
# 编译项目
mvn clean compile

# 运行项目
mvn spring-boot:run
```

或者：

```bash
# 打包项目
mvn clean package

# 运行jar包
java -jar target/websocket-demo-1.0.0.jar
```

### 3. 访问测试

启动成功后，访问以下地址：

- **测试页面**: http://localhost:8080
- **WebSocket端点**: ws://localhost:8080/websocket
- **服务状态API**: http://localhost:8080/api/websocket/status
- **健康检查API**: http://localhost:8080/api/websocket/health

## API接口

### WebSocket端点

- **地址**: `ws://localhost:8080/websocket`
- **协议**: WebSocket
- **功能**: 实时双向通信

### REST API

#### 1. 获取连接状态
```
GET /api/websocket/status
```

响应示例：
```json
{
  "connectionCount": 2,
  "timestamp": 1703123456789,
  "message": "WebSocket服务运行正常"
}
```

#### 2. 发送广播消息
```
POST /api/websocket/broadcast
Content-Type: application/json

{
  "message": "这是一条广播消息"
}
```

响应示例：
```json
{
  "success": true,
  "message": "广播消息发送完成",
  "sentCount": 2,
  "totalConnections": 2
}
```

#### 3. 健康检查
```
GET /api/websocket/health
```

响应示例：
```json
{
  "status": "UP",
  "service": "WebSocket Demo",
  "timestamp": 1703123456789
}
```

## 消息格式

### 客户端发送消息
客户端可以发送任意文本消息到服务器。

### 服务器响应消息
服务器返回JSON格式的消息：

#### 系统消息
```json
{
  "type": "system",
  "message": "连接成功！会话ID: abc123, 当前时间: 2023-12-21 10:30:45",
  "timestamp": "1703123456789"
}
```

#### 回显消息
```json
{
  "type": "echo",
  "original": "用户发送的消息",
  "response": "服务器收到消息: 用户发送的消息",
  "timestamp": "1703123456789",
  "sessionId": "abc123"
}
```

#### 广播消息
```json
{
  "type": "broadcast",
  "from": "发送者会话ID",
  "message": "广播的消息内容",
  "timestamp": "1703123456789"
}
```

#### 管理员广播
```json
{
  "type": "admin_broadcast",
  "message": "管理员发送的广播消息",
  "timestamp": "1703123456789"
}
```

## 测试方法

### 1. 使用Web测试页面
1. 启动项目后访问 http://localhost:8080
2. 点击"连接"按钮建立WebSocket连接
3. 在消息输入框中输入消息并发送
4. 观察消息的发送和接收

### 2. 使用WebSocket客户端工具
推荐使用以下工具进行测试：
- WebSocket King (Chrome扩展)
- Postman (支持WebSocket)
- wscat (命令行工具)

### 3. 使用JavaScript代码
```javascript
const ws = new WebSocket('ws://localhost:8080/websocket');

ws.onopen = function(event) {
    console.log('连接已建立');
    ws.send('Hello WebSocket!');
};

ws.onmessage = function(event) {
    console.log('收到消息:', event.data);
};

ws.onclose = function(event) {
    console.log('连接已关闭');
};
```

## 配置说明

主要配置项在 `application.properties` 中：

```properties
# 服务端口
server.port=8080

# WebSocket消息大小限制
spring.websocket.message-size-limit=8192
spring.websocket.binary-message-size-limit=8192

# 日志级别
logging.level.com.example.websocket=DEBUG
```

## 注意事项

1. **跨域配置**: 项目已配置允许所有域名访问，生产环境请根据需要调整
2. **连接管理**: 服务器会自动管理连接的建立和关闭
3. **消息广播**: 支持向所有连接的客户端广播消息
4. **错误处理**: 包含完整的错误处理和日志记录

## 扩展功能

可以基于此项目扩展以下功能：
- 用户认证和授权
- 房间/频道管理
- 消息持久化
- 集群部署支持
- 消息加密
- 心跳检测

## 许可证

MIT License
