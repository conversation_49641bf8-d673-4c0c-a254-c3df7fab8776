# 应用基本配置
spring.application.name=websocket-demo
server.port=8080

# 日志配置
logging.level.com.example.websocket=DEBUG
logging.level.org.springframework.web.socket=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n

# WebSocket配置
# 设置消息缓冲区大小 (8KB)
spring.websocket.message-size-limit=8192
# 设置二进制消息缓冲区大小 (8KB)  
spring.websocket.binary-message-size-limit=8192

# HTTP编码配置
server.servlet.encoding.charset=UTF-8
server.servlet.encoding.enabled=true
server.servlet.encoding.force=true

# 静态资源配置
spring.web.resources.static-locations=classpath:/static/
spring.mvc.static-path-pattern=/**
