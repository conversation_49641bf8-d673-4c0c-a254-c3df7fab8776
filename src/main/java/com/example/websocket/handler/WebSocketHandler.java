package com.example.websocket.handler;

import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * WebSocket 消息处理器
 *
 * <AUTHOR>
 * @version 1.0
 */
@Component
public class WebSocketHandler extends TextWebSocketHandler {

    // 存储所有连接的会话
    private static final ConcurrentHashMap<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    
    // 连接计数器
    private static final AtomicInteger connectionCount = new AtomicInteger(0);

    /**
     * 连接建立后调用
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        String sessionId = session.getId();
        sessions.put(sessionId, session);
        int count = connectionCount.incrementAndGet();
        
        System.out.println("WebSocket连接建立: " + sessionId + ", 当前连接数: " + count);
        
        // 向客户端发送连接成功消息
        String welcomeMessage = String.format(
            "{\"type\":\"system\",\"message\":\"连接成功！会话ID: %s, 当前时间: %s\",\"timestamp\":\"%s\"}",
            sessionId,
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")),
            System.currentTimeMillis()
        );
        session.sendMessage(new TextMessage(welcomeMessage));
    }

    /**
     * 接收到消息时调用
     */
    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String sessionId = session.getId();
        String payload = message.getPayload();
        
        System.out.println("收到来自 " + sessionId + " 的消息: " + payload);
        
        // 构造回复消息
        String responseMessage = String.format(
            "{\"type\":\"echo\",\"original\":\"%s\",\"response\":\"服务器收到消息: %s\",\"timestamp\":\"%s\",\"sessionId\":\"%s\"}",
            payload.replace("\"", "\\\""),
            payload.replace("\"", "\\\""),
            System.currentTimeMillis(),
            sessionId
        );
        
        // 回复消息给发送者
        session.sendMessage(new TextMessage(responseMessage));
        
        // 广播消息给所有其他连接（可选）
        broadcastMessage(sessionId, payload);
    }

    /**
     * 连接关闭后调用
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        String sessionId = session.getId();
        sessions.remove(sessionId);
        int count = connectionCount.decrementAndGet();
        
        System.out.println("WebSocket连接关闭: " + sessionId + ", 当前连接数: " + count + ", 关闭状态: " + status);
    }

    /**
     * 传输错误时调用
     */
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) throws Exception {
        String sessionId = session.getId();
        System.err.println("WebSocket传输错误: " + sessionId + ", 错误: " + exception.getMessage());
        
        if (session.isOpen()) {
            session.close();
        }
    }

    /**
     * 广播消息给所有其他连接
     */
    private void broadcastMessage(String senderSessionId, String message) {
        String broadcastMessage = String.format(
            "{\"type\":\"broadcast\",\"from\":\"%s\",\"message\":\"%s\",\"timestamp\":\"%s\"}",
            senderSessionId,
            message.replace("\"", "\\\""),
            System.currentTimeMillis()
        );
        
        sessions.forEach((sessionId, session) -> {
            if (!sessionId.equals(senderSessionId) && session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(broadcastMessage));
                } catch (IOException e) {
                    System.err.println("广播消息失败: " + sessionId + ", 错误: " + e.getMessage());
                }
            }
        });
    }

    /**
     * 获取当前连接数
     */
    public static int getConnectionCount() {
        return connectionCount.get();
    }

    /**
     * 获取所有会话
     */
    public static ConcurrentHashMap<String, WebSocketSession> getSessions() {
        return sessions;
    }
}
