package com.example.websocket.controller;

import com.example.websocket.handler.WebSocketHandler;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * WebSocket REST 控制器
 * 提供一些管理和测试接口
 * 
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/api/websocket")
@CrossOrigin(origins = "*")
public class WebSocketController {

    /**
     * 获取当前连接状态
     */
    @GetMapping("/status")
    public Map<String, Object> getStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("connectionCount", WebSocketHandler.getConnectionCount());
        status.put("timestamp", System.currentTimeMillis());
        status.put("message", "WebSocket服务运行正常");
        return status;
    }

    /**
     * 向所有连接发送消息
     */
    @PostMapping("/broadcast")
    public Map<String, Object> broadcast(@RequestBody Map<String, String> request) {
        String message = request.get("message");
        if (message == null || message.trim().isEmpty()) {
            Map<String, Object> error = new HashMap<>();
            error.put("success", false);
            error.put("message", "消息内容不能为空");
            return error;
        }

        String broadcastMessage = String.format(
            "{\"type\":\"admin_broadcast\",\"message\":\"%s\",\"timestamp\":\"%s\"}",
            message.replace("\"", "\\\""),
            System.currentTimeMillis()
        );

        int sentCount = 0;
        for (WebSocketSession session : WebSocketHandler.getSessions().values()) {
            if (session.isOpen()) {
                try {
                    session.sendMessage(new TextMessage(broadcastMessage));
                    sentCount++;
                } catch (IOException e) {
                    System.err.println("发送广播消息失败: " + e.getMessage());
                }
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "广播消息发送完成");
        result.put("sentCount", sentCount);
        result.put("totalConnections", WebSocketHandler.getConnectionCount());
        return result;
    }

    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("service", "WebSocket Demo");
        health.put("timestamp", System.currentTimeMillis());
        return health;
    }
}
