package com.example.websocket;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * WebSocket Demo 应用主启动类
 * 
 * <AUTHOR>
 * @version 1.0
 */
@SpringBootApplication
public class WebSocketDemoApplication {

    public static void main(String[] args) {
        SpringApplication.run(WebSocketDemoApplication.class, args);
        System.out.println("WebSocket Demo 应用启动成功！");
        System.out.println("访问地址: http://localhost:8080");
        System.out.println("WebSocket端点: ws://localhost:8080/websocket");
    }
}
