<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Demo 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .controls {
            margin: 20px 0;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        input[type="text"] {
            width: 70%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
            margin: 20px 0;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .system { background-color: #e2e3e5; }
        .sent { background-color: #d1ecf1; }
        .received { background-color: #d4edda; }
        .broadcast { background-color: #fff3cd; }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket Demo 测试页面</h1>
        
        <div id="status" class="status disconnected">
            状态: 未连接
        </div>
        
        <div class="controls">
            <button id="connectBtn" onclick="connect()">连接</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>断开连接</button>
            <button onclick="getStatus()">获取服务状态</button>
            <button onclick="clearMessages()">清空消息</button>
        </div>
        
        <div class="controls">
            <input type="text" id="messageInput" placeholder="输入要发送的消息..." onkeypress="handleKeyPress(event)">
            <button id="sendBtn" onclick="sendMessage()" disabled>发送消息</button>
        </div>
        
        <div class="controls">
            <input type="text" id="broadcastInput" placeholder="输入广播消息...">
            <button onclick="sendBroadcast()">发送广播</button>
        </div>
        
        <div id="messages" class="messages"></div>
    </div>

    <script>
        let websocket = null;
        let isConnected = false;

        function connect() {
            if (websocket !== null) {
                return;
            }

            const wsUrl = 'ws://localhost:8080/websocket';
            websocket = new WebSocket(wsUrl);

            websocket.onopen = function(event) {
                isConnected = true;
                updateStatus('已连接', 'connected');
                updateButtons();
                addMessage('系统', '连接到服务器成功', 'system');
            };

            websocket.onmessage = function(event) {
                try {
                    const data = JSON.parse(event.data);
                    let messageType = 'received';
                    let sender = '服务器';
                    
                    if (data.type === 'system') {
                        messageType = 'system';
                        sender = '系统';
                    } else if (data.type === 'broadcast') {
                        messageType = 'broadcast';
                        sender = '广播';
                    } else if (data.type === 'admin_broadcast') {
                        messageType = 'broadcast';
                        sender = '管理员广播';
                    }
                    
                    addMessage(sender, data.message || event.data, messageType);
                } catch (e) {
                    addMessage('服务器', event.data, 'received');
                }
            };

            websocket.onclose = function(event) {
                isConnected = false;
                updateStatus('连接已关闭', 'disconnected');
                updateButtons();
                addMessage('系统', '连接已关闭', 'system');
                websocket = null;
            };

            websocket.onerror = function(event) {
                addMessage('系统', '连接错误', 'system');
                console.error('WebSocket错误:', event);
            };
        }

        function disconnect() {
            if (websocket !== null) {
                websocket.close();
            }
        }

        function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (message === '' || !isConnected) {
                return;
            }

            websocket.send(message);
            addMessage('我', message, 'sent');
            input.value = '';
        }

        function sendBroadcast() {
            const input = document.getElementById('broadcastInput');
            const message = input.value.trim();
            
            if (message === '') {
                return;
            }

            fetch('/api/websocket/broadcast', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    addMessage('系统', `广播消息发送成功，发送给 ${data.sentCount} 个连接`, 'system');
                } else {
                    addMessage('系统', `广播消息发送失败: ${data.message}`, 'system');
                }
            })
            .catch(error => {
                addMessage('系统', '发送广播消息时出错', 'system');
                console.error('Error:', error);
            });
            
            input.value = '';
        }

        function getStatus() {
            fetch('/api/websocket/status')
            .then(response => response.json())
            .then(data => {
                addMessage('系统', `服务状态: ${data.message}, 当前连接数: ${data.connectionCount}`, 'system');
            })
            .catch(error => {
                addMessage('系统', '获取服务状态失败', 'system');
                console.error('Error:', error);
            });
        }

        function updateStatus(text, className) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = '状态: ' + text;
            statusDiv.className = 'status ' + className;
        }

        function updateButtons() {
            document.getElementById('connectBtn').disabled = isConnected;
            document.getElementById('disconnectBtn').disabled = !isConnected;
            document.getElementById('sendBtn').disabled = !isConnected;
        }

        function addMessage(sender, message, type) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message ' + type;
            
            const timestamp = new Date().toLocaleTimeString();
            messageDiv.innerHTML = `<strong>[${timestamp}] ${sender}:</strong> ${message}`;
            
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 页面加载完成后自动连接
        window.onload = function() {
            addMessage('系统', '页面加载完成，点击"连接"按钮开始测试', 'system');
        };
    </script>
</body>
</html>
